package my.com.cmg.rms.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import lombok.AllArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;

@AllArgsConstructor
public class UserIdFilter extends OncePerRequestFilter {

  private final SecUserService userService;

  @Override
  protected void doFilterInternal(
      @NonNull HttpServletRequest request,
      @NonNull HttpServletResponse response,
      @NonNull FilterChain filterChain)
      throws ServletException, IOException {

    String userIdHeader = request.getHeader("X-Ref-Id");

    if (userIdHeader != null) {
      try {
        Long userId = Long.parseLong(userIdHeader);
        SecUserDTO user = userService.getByUserId(userId);

        if (user != null) {
          CustomSession authentication = new CustomSession(user);
          authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
          SecurityContextHolder.getContext().setAuthentication(authentication);
          filterChain.doFilter(request, response);
          return;
        }
      } catch (Exception ex) {
        // log or ignore parsing issue
      }
    }

    SecurityContextHolder.clearContext();
    response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid or missing X-Ref-Id header");
  }
}
