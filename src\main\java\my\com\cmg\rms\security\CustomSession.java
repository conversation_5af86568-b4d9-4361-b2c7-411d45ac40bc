package my.com.cmg.rms.security;

import java.util.Optional;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

public class CustomSession extends AbstractAuthenticationToken {

  private final SecUserDTO securityUser;

  public CustomSession(SecUserDTO securityUser) {
    super(null);
    this.securityUser = securityUser;
    setAuthenticated(true);
  }

  @Override
  public SecUserDTO getPrincipal() {
    return securityUser;
  }

  @Override
  public Object getCredentials() {
    return null;
  }

  public static Optional<CustomSession> getSession() {
    return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication())
        .filter(CustomSession.class::isInstance)
        .map(CustomSession.class::cast);
  }

  public static Optional<SecUserDTO> getSecurityUser() {
    return getSession().map(CustomSession::getPrincipal);
  }
}
