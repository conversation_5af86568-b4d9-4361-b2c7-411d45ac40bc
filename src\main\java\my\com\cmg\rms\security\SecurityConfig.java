package my.com.cmg.rms.security;

import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.oauth2.jwt.JwtDecoders;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@AllArgsConstructor(onConstructor_ = {@Autowired})
public class SecurityConfig {

  private final String issuerUri;
  private final String developmentMode;
  private final SecUserService userService;

  public SecurityConfig(
      @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}") String issuerUri,
      @Value("${rms.development.mode}") String developmentMode,
      SecUserService userService) {
    this.issuerUri = issuerUri;
    this.developmentMode = developmentMode;
    this.userService = userService;
  }

  @Bean
  public SecurityFilterChain configure(HttpSecurity http) throws Exception {
    if (developmentMode.equals("true")) {
      http.authorizeHttpRequests(authorize -> authorize.anyRequest().permitAll());
    } else {
      http.authorizeHttpRequests(authorize -> authorize.anyRequest().authenticated())
          .oauth2ResourceServer(
              oauth2 ->
                  oauth2.jwt(
                      jwt -> {
                        jwt.decoder(JwtDecoders.fromIssuerLocation(issuerUri));
                        jwt.jwtAuthenticationConverter(
                            new RoleConverter()); // or new CustomJwtConverter(userService)
                      }));
    }

    http.csrf(csrf -> csrf.disable());
    return http.build();
  }

  @Bean
  public FilterRegistrationBean<TenantFilter> tenantFilter() {
    FilterRegistrationBean<TenantFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(new TenantFilter());
    registrationBean.addUrlPatterns("/*");
    registrationBean.setEnabled(!developmentMode.equals("true"));
    return registrationBean;
  }

  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    return http.csrf()
        .disable()
        .authorizeHttpRequests(auth -> auth.anyRequest().authenticated())
        .addFilterBefore(new UserIdFilter(userService), UsernamePasswordAuthenticationFilter.class)
        .build();
  }
}
