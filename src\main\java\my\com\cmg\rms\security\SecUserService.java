package my.com.cmg.rms.security;

import org.springframework.stereotype.Service;

@Service
public class SecUserService {

  private final SecUserRepositoryJooq userRepositoryJooq;

  public SecUserService(SecUserRepositoryJooq userRepositoryJooq) {
    this.userRepositoryJooq = userRepositoryJooq;
  }

  public SecUserDTO getByUserId(Long userId) {
    return userRepositoryJooq.findByUserId(userId);
  }
}
