package my.com.cmg.rms.security;

import static org.jooq.impl.DSL.concat;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.inline;
import static org.jooq.impl.DSL.ltrim;
import static org.jooq.impl.DSL.rtrim;
import static org.jooq.impl.DSL.select;
import static org.jooq.impl.DSL.table;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.DSLContext;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
@AllArgsConstructor
public class SecUserRepositoryJooq {

  private final DSLContext dsl;

  public SecUserDTO findByUserId(Long userId) {
    Select<?> query =
        dsl.select(
                field("SU.usr_id", Long.class),
                concat(
                    ltrim(field("SU.usr_firstname", String.class)),
                    inline(" "),
                    rtrim(field("SU.usr_lastname", String.class))),
                field("SU.usr_firstname", String.class),
                field("SU.usr_lastname", String.class),
                field("SU.usr_email", String.class),
                field("UD.usr_contact", String.class),
                field("UD.usr_designation", String.class),
                field("RC.rc_desc", String.class),
                field("UD.rqstr_seqno", Long.class),
                field("RU.rqstr_code", String.class),
                field("RU.rqstr_desc", String.class),
                field("RG.group_name", String.class),
                field("RG.unit_group_level", Long.class),
                field("UD.dept_seqno", Long.class),
                field("DEPT.dept_desc", String.class),
                field(
                    select(field("fc_i_cur_fin_cyc", Long.class))
                        .from(table("rm_facility_details"))),
                field(
                    select(field("fc_i_category", String.class))
                        .from(table("rm_facility_details"))),
                field("PRES.prescriber_seqno", Long.class))
            .from(TableUtil.table("rm_sec_user", "SU"))
            .leftJoin(TableUtil.table("rm_user_details", "UD"))
            .on(field("SU.usr_id").eq(field("UD.usr_id")))
            .leftJoin(TableUtil.table("rm_requester_units", "RU"))
            .on(field("UD.rqstr_seqno").eq(field("RU.rqstr_seqno")))
            .leftJoin(TableUtil.table("rm_requester_groups", "RG"))
            .on(field("RU.group_seqno").eq(field("RG.group_seqno")))
            .leftJoin(TableUtil.table("rm_departments", "DEPT"))
            .on(field("UD.dept_seqno").eq(field("DEPT.dept_seqno")))
            .leftJoin(TableUtil.table("rm_prescribers", "PRES"))
            .on(field("PRES.usr_id").eq(field("SU.usr_id")))
            .leftJoin(TableUtil.table("rm_ref_codes", "RC"))
            .on(
                field("UD.usr_designation")
                    .eq(field("RC.rc_value"))
                    .and(field("RC.rc_domain").eq("DESIGNATION")))
            .where(field("SU.usr_id").eq(userId));

    log.info(LogUtil.QUERY, query);
    return query.fetchOneInto(SecUserDTO.class);
  }
}
