package my.com.cmg.rms.security;

import org.springframework.stereotype.Repository;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Repository
public class SecUserRepositoryJooq {

  private final WebClient webClient;

  public SecUserRepositoryJooq(WebClient.Builder builder) {
    this.webClient = builder.baseUrl("http://central-service-url/api/v1/mers/secure/user").build();
  }

  public SecUserDTO findByUserId(Long userId) {
    return webClient
        .get()
        .uri("/user/{id}", userId)
        .retrieve()
        .bodyToMono(SecUserDTO.class)
        .onErrorResume(
            e -> {
              // Log error or fallback
              System.out.println("Error fetching user from central: " + e.getMessage());
              return Mono.empty(); // or return default user object
            })
        .block(); // Gunakan async jika tak nak block thread
  }
}
